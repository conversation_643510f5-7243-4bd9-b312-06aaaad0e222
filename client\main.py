import sys
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QComboBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap
import cv2
import requests


class VideoThread(QThread):
    change_pixmap = pyqtSignal(QImage)
    update_counts = pyqtSignal(dict)
    connection_error = pyqtSignal(str)

    def __init__(self, rtmp_url, api_url):
        super().__init__()
        self.rtmp_url = rtmp_url
        self.api_url = api_url
        self.running = False
        self.cap = None

    def run(self):
        self.running = True

        # 等待RTMP流可用
        max_retries = 10
        retry_count = 0

        while retry_count < max_retries and self.running:
            try:
                # 使用FFmpeg后端连接RTMP流，应用project3的低延时优化
                self.cap = cv2.VideoCapture(self.rtmp_url, cv2.CAP_FFMPEG)

                if self.cap.isOpened():
                    # project3优化: 设置低延时参数
                    self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲
                    # 额外的低延时设置
                    self.cap.set(cv2.CAP_PROP_FPS, 30)  # 确保帧率匹配
                    print(f"成功连接到RTMP流: {self.rtmp_url}")
                    break
                else:
                    if self.cap:
                        self.cap.release()
                    retry_count += 1
                    print(f"连接RTMP流失败，重试 {retry_count}/{max_retries}")
                    self.msleep(3000)  # 等待3秒后重试，给FFmpeg更多时间建立连接

            except Exception as e:
                retry_count += 1
                print(f"RTMP连接异常: {e}, 重试 {retry_count}/{max_retries}")
                self.msleep(3000)

        if retry_count >= max_retries:
            self.connection_error.emit("无法连接到RTMP流，请检查服务器状态")
            return

        frame_count = 0
        while self.running and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if ret:
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
                self.change_pixmap.emit(qt_image)

                frame_count += 1
                # 每30帧获取一次计数
                if frame_count % 30 == 0:
                    self.get_counts()
            else:
                print("读取RTMP帧失败")
                self.msleep(100)  # 短暂等待

        if self.cap:
            self.cap.release()

    def get_counts(self):
        try:
            response = requests.get(f"{self.api_url}/status")
            if response.status_code == 200:
                data = response.json()
                self.update_counts.emit(data.get('counts', {}))
        except Exception as e:
            print(f"获取计数错误: {e}")

    def stop(self):
        self.running = False
        self.wait()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("电子元器件计数系统")
        self.setGeometry(100, 100, 800, 600)

        # 配置
        self.rtmp_url = "rtmp://localhost/live/stream"
        self.api_url = "http://localhost:5000/api"

        # 初始化UI
        self.init_ui()

        # 视频线程
        self.video_thread = None
        self.detection_running = False

        # 状态检查定时器 - 只在检测运行时启用
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_server_status)
        # 不自动启动定时器，只在检测开始时启动

    def init_ui(self):
        # 主部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 视频显示区域
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(640, 640)
        self.video_label.setStyleSheet("background-color: black;")
        main_layout.addWidget(self.video_label)

        # 控制面板
        control_panel = QHBoxLayout()

        # 摄像头选择
        self.camera_combo = QComboBox()
        self.camera_combo.addItems(["摄像头0", "摄像头1", "摄像头2"])
        control_panel.addWidget(QLabel("选择摄像头:"))
        control_panel.addWidget(self.camera_combo)

        # 分辨率选择
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["480x480", "640x640", "800x600"])
        control_panel.addWidget(QLabel("分辨率:"))
        control_panel.addWidget(self.resolution_combo)

        # 帧率选择
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["15", "24", "30", "60"])
        control_panel.addWidget(QLabel("帧率:"))
        control_panel.addWidget(self.fps_combo)

        main_layout.addLayout(control_panel)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 开始按钮
        self.start_btn = QPushButton("开始检测")
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.start_btn.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止检测")
        self.stop_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.stop_btn.clicked.connect(self.stop_detection)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        main_layout.addLayout(button_layout)

        # 计数显示区域
        self.count_layout = QHBoxLayout()
        self.count_labels = {}

        # 初始化计数标签
        for component in ["电阻", "电容", "LED"]:  # 根据实际模型类别修改
            label = QLabel(f"{component}: 0")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-size: 16px; font-weight: bold;")
            self.count_layout.addWidget(label)
            self.count_labels[component] = label

        main_layout.addLayout(self.count_layout)

        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("准备就绪")

    def start_detection(self):
        # 获取参数
        camera_index = self.camera_combo.currentIndex()
        resolution = self.resolution_combo.currentText()
        fps = int(self.fps_combo.currentText())

        # 发送API请求
        try:
            response = requests.post(
                f"{self.api_url}/start",
                json={
                    "camera_index": camera_index,
                    "resolution": resolution,
                    "fps": fps
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.detection_running = True
                    self.status_bar.showMessage(f"检测已启动 - RTMP: {data.get('rtmp_url', '')}")

                    # 启动状态监控定时器
                    self.status_timer.start(2000)

                    # 等待一下让服务器准备好RTMP流
                    QTimer.singleShot(3000, self.start_video_thread)

                    # 更新按钮状态
                    self.start_btn.setEnabled(False)
                    self.stop_btn.setEnabled(True)
                else:
                    self.status_bar.showMessage(f"启动失败: {data.get('message', '未知错误')}")
            else:
                self.status_bar.showMessage(f"启动失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"连接服务器失败: {str(e)}")

    def start_video_thread(self):
        """启动视频线程"""
        if self.video_thread is None and self.detection_running:
            self.video_thread = VideoThread(self.rtmp_url, self.api_url)
            self.video_thread.change_pixmap.connect(self.set_image)
            self.video_thread.update_counts.connect(self.update_count_labels)
            self.video_thread.connection_error.connect(self.handle_connection_error)
            self.video_thread.start()
            self.status_bar.showMessage("正在连接视频流...")

    def handle_connection_error(self, error_msg):
        """处理连接错误"""
        self.status_bar.showMessage(f"视频连接错误: {error_msg}")
        if self.video_thread:
            self.video_thread.stop()
            self.video_thread = None

    def stop_detection(self):
        try:
            response = requests.get(f"{self.api_url}/stop")

            if response.status_code == 200:
                self.detection_running = False
                self.status_bar.showMessage("检测已停止")

                # 停止状态监控定时器
                self.status_timer.stop()

                # 停止视频线程
                if self.video_thread is not None:
                    self.video_thread.stop()
                    self.video_thread = None

                # 更新按钮状态
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

                # 清空视频显示
                self.video_label.clear()
                self.video_label.setStyleSheet("background-color: black;")
                self.video_label.setText("视频显示区域")

            else:
                self.status_bar.showMessage(f"停止失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"连接服务器失败: {str(e)}")

    def set_image(self, image):
        self.video_label.setPixmap(QPixmap.fromImage(image).scaled(
            self.video_label.width(),
            self.video_label.height(),
            Qt.KeepAspectRatio
        ))

    def update_count_labels(self, counts):
        for component, count in counts.items():
            if component in self.count_labels:
                self.count_labels[component].setText(f"{component}: {count}")
            else:
                # 动态添加新组件计数
                label = QLabel(f"{component}: {count}")
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("font-size: 16px; font-weight: bold;")
                self.count_layout.addWidget(label)
                self.count_labels[component] = label

    def check_server_status(self):
        """检查服务器状态，只在检测运行时更新计数"""
        if not self.detection_running:
            return

        try:
            response = requests.get(f"{self.api_url}/status", timeout=2)
            if response.status_code == 200:
                data = response.json()
                server_status = data.get('status', 'unknown')

                if server_status == 'running':
                    # 检测正在运行，更新计数
                    counts = data.get('counts', {})
                    if counts:
                        self.update_count_labels(counts)
                elif server_status == 'stopped':
                    # 服务器端检测已停止，同步客户端状态
                    self.detection_running = False
                    self.status_timer.stop()
                    if self.video_thread:
                        self.video_thread.stop()
                        self.video_thread = None
                    self.start_btn.setEnabled(True)
                    self.stop_btn.setEnabled(False)
                    self.status_bar.showMessage("检测已停止")
        except Exception as e:
            # 连接异常，静默处理
            pass

    def closeEvent(self, event):
        if self.video_thread is not None:
            self.video_thread.stop()
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
