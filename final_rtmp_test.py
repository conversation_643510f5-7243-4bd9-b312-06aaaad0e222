#!/usr/bin/env python3
"""
最终RTMP流测试 - 30fps低延迟
"""

import requests
import cv2
import time

def test_rtmp_system():
    """完整的RTMP系统测试"""
    base_url = "http://localhost:5000/api"
    rtmp_url = "rtmp://localhost/live/stream"
    
    print("🚀 YOLOv8 30fps低延迟RTMP系统测试")
    print("=" * 60)
    
    # 1. 启动检测
    print("\n1️⃣ 启动30fps检测...")
    try:
        data = {
            "camera_index": 0,
            "resolution": "640x640", 
            "fps": 30
        }
        response = requests.post(f"{base_url}/start", json=data)
        result = response.json()
        print(f"启动结果: {result}")
        
        if result.get('status') != 'success':
            print("❌ 启动失败!")
            return False
            
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        return False
    
    # 2. 等待流建立
    print("\n2️⃣ 等待RTMP流建立...")
    for i in range(8):
        print(f"等待中... {i+1}/8")
        time.sleep(1)
    
    # 3. 测试RTMP连接
    print("\n3️⃣ 测试RTMP连接...")
    
    try:
        # 使用FFmpeg后端连接
        cap = cv2.VideoCapture(rtmp_url, cv2.CAP_FFMPEG)
        
        if not cap.isOpened():
            print("❌ 无法连接到RTMP流")
            return False
        
        print("✅ 成功连接到RTMP流!")
        
        # 设置低延时参数
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # 测试读取帧
        success_count = 0
        total_frames = 30  # 测试1秒
        
        print(f"\n4️⃣ 测试读取{total_frames}帧...")
        start_time = time.time()
        
        for i in range(total_frames):
            ret, frame = cap.read()
            
            if ret:
                success_count += 1
                if i % 10 == 0:
                    print(f"✅ 成功读取第{i+1}帧, 分辨率: {frame.shape}")
            else:
                print(f"❌ 第{i+1}帧读取失败")
            
            time.sleep(1/30)  # 30fps间隔
        
        cap.release()
        
        # 计算性能
        elapsed_time = time.time() - start_time
        actual_fps = success_count / elapsed_time
        
        print(f"\n📊 性能统计:")
        print(f"   成功帧数: {success_count}/{total_frames}")
        print(f"   实际帧率: {actual_fps:.1f} fps")
        print(f"   成功率: {success_count/total_frames*100:.1f}%")
        
        if success_count >= total_frames * 0.8:  # 80%成功率
            print("✅ RTMP流性能良好!")
            rtmp_success = True
        else:
            print("⚠️ RTMP流性能不佳")
            rtmp_success = False
            
    except Exception as e:
        print(f"❌ RTMP测试异常: {e}")
        rtmp_success = False
    
    # 5. 检查检测状态
    print("\n5️⃣ 检查检测状态...")
    try:
        response = requests.get(f"{base_url}/status")
        status = response.json()
        print(f"检测状态: {status}")
        
        if status.get('status') == 'running':
            counts = status.get('counts', {})
            if counts:
                print(f"✅ 检测到元器件: {counts}")
            else:
                print("⚠️ 暂未检测到元器件")
        else:
            print("❌ 检测未运行")
            
    except Exception as e:
        print(f"❌ 状态检查错误: {e}")
    
    # 6. 停止检测
    print("\n6️⃣ 停止检测...")
    try:
        response = requests.get(f"{base_url}/stop")
        result = response.json()
        print(f"停止结果: {result}")
    except Exception as e:
        print(f"❌ 停止错误: {e}")
    
    # 7. 总结
    print("\n" + "=" * 60)
    if rtmp_success:
        print("🎉 系统测试成功!")
        print("✅ API正常")
        print("✅ 检测功能正常") 
        print("✅ RTMP流正常")
        print("✅ 30fps低延迟达标")
        print("\n💡 现在可以通过客户端GUI正常使用系统")
        return True
    else:
        print("❌ 系统测试失败")
        print("✅ API正常")
        print("✅ 检测功能正常")
        print("❌ RTMP流有问题")
        return False

if __name__ == "__main__":
    test_rtmp_system()
