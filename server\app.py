from flask import Flask, request
from flask_restx import Api, Resource, fields
from utils.camera import CameraManager
from utils.stream import StreamManager
from utils.detection import Detector
from utils.adaptive_fps import AdaptiveFPSController, FrameRateMonitor
import threading
import config
import logging
import time
from datetime import datetime

app = Flask(__name__)
api = Api(app, version='1.0', title='Stream API', description='视频流处理API')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)
logger = logging.getLogger(__name__)

# 初始化组件
camera_manager = CameraManager()
stream_manager = StreamManager(config.RTMP_URL)
detector = Detector(config.MODEL_PATH)

# 全局状态
processing = False
stream_thread = None

# 数据模型定义
start_model = api.model('StartStream', {
    'camera_index': fields.Integer(default=0, description='摄像头索引'),
    'resolution': fields.String(default='640x640', description='分辨率'),
    'fps': fields.Integer(default=30, description='帧率'),
    'confidence_threshold': fields.Float(default=0.7, description='置信度阈值'),
    'classes': fields.List(fields.Integer, description='检测类别')
})

status_model = api.model('Status', {
    'status': fields.String(description='运行状态'),
    'counts': fields.Raw(description='检测计数'),
    'message': fields.String(description='附加信息'),
    'rtmp_url': fields.String(description='RTMP流地址')
})


@api.route('/api/start')
class StartStream(Resource):
    @api.expect(start_model)
    @api.marshal_with(status_model)
    def post(self):
        """启动视频流处理"""
        global processing, stream_thread

        logger.info(f"Received start request with data: {request.json}")

        if processing:
            logger.warning("Start request rejected: stream already running")
            return {"status": "error", "message": "Stream is already running"}, 400

        # 获取参数
        data = request.json
        camera_index = data.get('camera_index', 0)
        resolution = data.get('resolution', '640x640')
        fps = data.get('fps', 30)

        logger.info(f"Starting stream with params - camera: {camera_index}, resolution: {resolution}, fps: {fps}")

        # 启动处理线程
        processing = True
        stream_thread = threading.Thread(
            target=process_stream,
            args=(camera_index, resolution, fps),
            name=f"StreamThread-{datetime.now().strftime('%Y%m%d%H%M%S')}")
        stream_thread.start()

        logger.info(f"Stream started successfully, RTMP URL: {config.RTMP_URL}")
        return {
            "status": "success",
            "rtmp_url": config.RTMP_URL,
            "message": "Stream started"
        }


@api.route('/api/stop')
class StopStream(Resource):
    @api.marshal_with(status_model)
    def get(self):
        """停止视频流处理"""
        global processing, stream_thread

        logger.info("Received stop request")

        if not processing:
            logger.warning("Stop request rejected: no stream running")
            return {"status": "error", "message": "No stream is running"}, 400

        logger.info("Stopping stream...")
        processing = False
        if stream_thread:
            stream_thread.join()
            logger.info("Stream thread joined successfully")

        logger.info("Stream stopped successfully")
        return {"status": "success", "message": "Stream stopped"}


@api.route('/api/status')
class StreamStatus(Resource):
    @api.marshal_with(status_model)
    def get(self):
        """获取当前状态"""
        logger.info("Received status request")
        counts = detector.get_last_counts()
        status = "running" if processing else "stopped"
        logger.info(f"Current status: {status}, counts: {counts}")
        return {
            "status": status,
            "counts": counts
        }


def process_stream(camera_index, resolution, fps):
    """视频流处理线程函数 - 集成低延时优化"""
    thread_name = threading.current_thread().name
    logger.info(f"{thread_name} - Starting optimized stream processing")

    # 初始化自适应FPS控制器
    fps_controller = AdaptiveFPSController(
        initial_fps=fps,
        min_fps=config.LatencyOptimization.MIN_FPS,
        max_fps=config.LatencyOptimization.MAX_FPS,
        target_latency_ms=config.LatencyOptimization.TARGET_LATENCY_MS
    )

    # 初始化帧率监控器
    fps_monitor = FrameRateMonitor()

    # 初始化摄像头
    camera = camera_manager.get_camera(camera_index, resolution, fps)
    logger.info(f"{thread_name} - Camera initialized")

    # 初始化FFmpeg推流
    streamer = stream_manager.get_streamer(resolution, fps)
    logger.info(f"{thread_name} - Streamer initialized")

    frame_count = 0
    last_fps_update = time.time()

    try:
        while processing:
            frame_start_time = time.time()

            # 读取帧
            ret, frame = camera.read()
            if not ret:
                logger.error(f"{thread_name} - Failed to read frame from camera")
                break

            frame_count += 1
            fps_monitor.record_frame()

            try:
                # 检测电子元器件 - 记录检测时间
                detection_start = time.time()
                detected_frame, _ = detector.detect(frame)
                detection_time = (time.time() - detection_start) * 1000
                fps_controller.record_detection_time(detection_time)

                # 推流 - 记录编码时间
                encoding_start = time.time()
                streamer.process_frame(detected_frame)
                encoding_time = (time.time() - encoding_start) * 1000
                fps_controller.record_encoding_time(encoding_time)

                # 记录总处理时间
                total_time = (time.time() - frame_start_time) * 1000
                fps_controller.record_total_time(total_time)

            except Exception as e:
                logger.warning(f"{thread_name} - Frame processing error: {str(e)}")

            # 自适应帧率控制
            current_time = time.time()
            if current_time - last_fps_update > 2.0:  # 每2秒检查一次
                recommended_fps = fps_controller.get_current_fps()
                actual_fps = fps_monitor.get_actual_fps()

                # 如果推荐帧率变化较大，重新初始化流
                if abs(recommended_fps - fps) > 5:
                    logger.info(f"FPS变化较大，重新初始化流: {fps} -> {recommended_fps}")
                    # 这里可以选择重新初始化流或者动态调整

                last_fps_update = current_time

            # 性能监控和日志
            if config.PERFORMANCE_MONITORING and frame_count % config.LOG_INTERVAL_FRAMES == 0:
                current_time = time.time()
                actual_fps = fps_monitor.get_actual_fps()
                stats = fps_controller.get_performance_stats()

                logger.info(f"{thread_name} - 性能统计 [帧数: {frame_count}]")
                logger.info(f"  实际FPS: {actual_fps:.1f}, 推荐FPS: {stats.get('current_fps', fps)}")
                logger.info(f"  平均延迟: {stats.get('avg_total_latency', 0):.1f}ms")
                logger.info(f"  检测延迟: {stats.get('avg_detection_latency', 0):.1f}ms")
                logger.info(f"  编码延迟: {stats.get('avg_encoding_latency', 0):.1f}ms")

            # 帧率控制 - 避免过快处理
            frame_interval = fps_controller.get_frame_interval()
            elapsed = time.time() - frame_start_time
            if elapsed < frame_interval:
                time.sleep(frame_interval - elapsed)

    except Exception as e:
        logger.error(f"{thread_name} - Error in stream processing: {str(e)}", exc_info=True)
    finally:
        camera.release()
        streamer.release()

        # 输出最终统计
        final_stats = fps_controller.get_performance_stats()
        logger.info(f"{thread_name} - 处理完成，总帧数: {frame_count}")
        logger.info(f"  最终FPS: {final_stats.get('current_fps', fps)}")
        logger.info(f"  平均延迟: {final_stats.get('avg_total_latency', 0):.1f}ms")


if __name__ == '__main__':
    logger.info("Starting YOLOv8 detection system with low-latency optimization...")
    logger.info(f"RTMP URL: {config.RTMP_URL}")
    logger.info(f"Model: {config.MODEL_PATH}")

    app.run(host='0.0.0.0', port=5000, threaded=True)
