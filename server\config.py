import os
import torch

# 模型路径 - 使用绝对路径
MODEL_PATH = os.path.join(os.path.dirname(__file__), 'models', 'best.pt')

# RTMP配置
RTMP_SERVER = 'rtmp://localhost/live'
RTMP_STREAM = 'stream'
RTMP_URL = f"{RTMP_SERVER}/{RTMP_STREAM}"

# 摄像头默认配置
DEFAULT_CAMERA_INDEX = 0
DEFAULT_RESOLUTION = '640x640'
DEFAULT_FPS = 30

# 低延时优化配置
class LatencyOptimization:
    # 自动检测硬件能力
    HAS_CUDA = torch.cuda.is_available()
    HAS_NVIDIA_GPU = False

    if HAS_CUDA:
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
            HAS_NVIDIA_GPU = result.returncode == 0
        except:
            pass

    # 动态帧率配置
    ADAPTIVE_FPS = True  # 启用自适应帧率
    MIN_FPS = 15  # 最低帧率
    MAX_FPS = 60  # 最高帧率
    TARGET_LATENCY_MS = 100  # 目标延迟(毫秒)

    # 编码优化
    USE_HARDWARE_ENCODING = HAS_NVIDIA_GPU  # 优先使用硬件编码
    ENABLE_FP16 = HAS_CUDA  # 启用半精度推理

    # 网络优化
    RTMP_CHUNK_SIZE = 128  # RTMP块大小
    BUFFER_SIZE_MS = 50  # 缓冲区大小(毫秒)

    # 检测优化
    DETECTION_CONFIDENCE = 0.5  # 检测置信度
    NMS_IOU_THRESHOLD = 0.45  # NMS IoU阈值
    MAX_DETECTIONS = 100  # 最大检测数量

# 性能监控配置
PERFORMANCE_MONITORING = True
LOG_INTERVAL_FRAMES = 100  # 每N帧输出一次性能日志

# 其他配置
DEBUG = True
