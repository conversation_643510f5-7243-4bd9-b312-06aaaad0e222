"""
自适应帧率控制器
根据系统性能动态调整帧率以维持低延迟
"""

import time
import logging
from collections import deque
import numpy as np

logger = logging.getLogger(__name__)

class AdaptiveFPSController:
    """
    自适应帧率控制器
    
    根据以下指标动态调整帧率：
    - 检测延迟
    - 编码延迟  
    - 系统负载
    - 目标延迟阈值
    """
    
    def __init__(self, initial_fps=30, min_fps=15, max_fps=60, target_latency_ms=100):
        self.current_fps = initial_fps
        self.min_fps = min_fps
        self.max_fps = max_fps
        self.target_latency_ms = target_latency_ms
        
        # 性能监控
        self.detection_times = deque(maxlen=50)  # 最近50帧的检测时间
        self.encoding_times = deque(maxlen=50)   # 最近50帧的编码时间
        self.total_times = deque(maxlen=50)      # 总处理时间
        
        # 控制参数
        self.adjustment_threshold = 0.1  # 调整阈值
        self.max_adjustment = 5  # 单次最大调整幅度
        self.stability_frames = 10  # 稳定帧数
        self.stable_count = 0
        
        # 统计信息
        self.frame_count = 0
        self.last_adjustment_time = time.time()
        self.adjustment_cooldown = 2.0  # 调整冷却时间(秒)
        
        logger.info(f"自适应FPS控制器初始化 - 初始FPS: {initial_fps}, "
                   f"范围: {min_fps}-{max_fps}, 目标延迟: {target_latency_ms}ms")
    
    def record_detection_time(self, detection_time_ms):
        """记录检测时间"""
        self.detection_times.append(detection_time_ms)
    
    def record_encoding_time(self, encoding_time_ms):
        """记录编码时间"""
        self.encoding_times.append(encoding_time_ms)
    
    def record_total_time(self, total_time_ms):
        """记录总处理时间"""
        self.total_times.append(total_time_ms)
        self.frame_count += 1
        
        # 每隔一定帧数检查是否需要调整
        if self.frame_count % 10 == 0:
            self._evaluate_performance()
    
    def _evaluate_performance(self):
        """评估性能并调整帧率"""
        current_time = time.time()
        
        # 检查冷却时间
        if current_time - self.last_adjustment_time < self.adjustment_cooldown:
            return
        
        if len(self.total_times) < 10:  # 数据不足
            return
        
        # 计算平均延迟
        avg_total_latency = np.mean(list(self.total_times))
        avg_detection_latency = np.mean(list(self.detection_times)) if self.detection_times else 0
        avg_encoding_latency = np.mean(list(self.encoding_times)) if self.encoding_times else 0
        
        # 计算延迟偏差
        latency_ratio = avg_total_latency / self.target_latency_ms
        
        logger.debug(f"性能评估 - 总延迟: {avg_total_latency:.1f}ms, "
                    f"检测: {avg_detection_latency:.1f}ms, "
                    f"编码: {avg_encoding_latency:.1f}ms, "
                    f"目标比率: {latency_ratio:.2f}")
        
        # 决定是否调整帧率
        adjustment = 0
        
        if latency_ratio > 1.2:  # 延迟过高，降低帧率
            adjustment = -min(self.max_adjustment, 
                            int((latency_ratio - 1.0) * 10))
            self.stable_count = 0
            
        elif latency_ratio < 0.8 and self.stable_count >= self.stability_frames:
            # 延迟较低且系统稳定，可以提高帧率
            adjustment = min(self.max_adjustment, 
                           int((1.0 - latency_ratio) * 5))
            self.stable_count = 0
            
        else:
            # 性能稳定
            self.stable_count += 1
        
        # 应用调整
        if adjustment != 0:
            old_fps = self.current_fps
            self.current_fps = max(self.min_fps, 
                                 min(self.max_fps, 
                                     self.current_fps + adjustment))
            
            if self.current_fps != old_fps:
                logger.info(f"FPS调整: {old_fps} -> {self.current_fps} "
                           f"(延迟: {avg_total_latency:.1f}ms)")
                self.last_adjustment_time = current_time
                
                # 清空历史数据，重新开始监控
                self.detection_times.clear()
                self.encoding_times.clear()
                self.total_times.clear()
    
    def get_current_fps(self):
        """获取当前推荐的帧率"""
        return self.current_fps
    
    def get_frame_interval(self):
        """获取帧间隔(秒)"""
        return 1.0 / self.current_fps
    
    def get_performance_stats(self):
        """获取性能统计信息"""
        if not self.total_times:
            return {}
        
        return {
            'current_fps': self.current_fps,
            'avg_total_latency': np.mean(list(self.total_times)),
            'avg_detection_latency': np.mean(list(self.detection_times)) if self.detection_times else 0,
            'avg_encoding_latency': np.mean(list(self.encoding_times)) if self.encoding_times else 0,
            'frame_count': self.frame_count,
            'stable_count': self.stable_count
        }
    
    def reset(self):
        """重置控制器状态"""
        self.detection_times.clear()
        self.encoding_times.clear()
        self.total_times.clear()
        self.frame_count = 0
        self.stable_count = 0
        self.last_adjustment_time = time.time()
        logger.info("自适应FPS控制器已重置")


class FrameRateMonitor:
    """帧率监控器 - 用于监控实际帧率"""
    
    def __init__(self, window_size=30):
        self.window_size = window_size
        self.frame_times = deque(maxlen=window_size)
        self.last_frame_time = time.time()
    
    def record_frame(self):
        """记录一帧"""
        current_time = time.time()
        if self.frame_times:
            interval = current_time - self.last_frame_time
            self.frame_times.append(interval)
        self.last_frame_time = current_time
    
    def get_actual_fps(self):
        """获取实际帧率"""
        if len(self.frame_times) < 2:
            return 0
        
        avg_interval = np.mean(list(self.frame_times))
        return 1.0 / avg_interval if avg_interval > 0 else 0
    
    def get_frame_drop_rate(self, target_fps):
        """计算丢帧率"""
        actual_fps = self.get_actual_fps()
        if target_fps <= 0:
            return 0
        
        return max(0, (target_fps - actual_fps) / target_fps)
