import cv2
import logging

logger = logging.getLogger(__name__)

class CameraManager:
    def __init__(self):
        self.cameras = {}

    def get_camera(self, index, resolution='640x640', fps=30):
        camera_key = f"{index}_{resolution}_{fps}"

        if camera_key in self.cameras:
            return self.cameras[camera_key]

        # 解析分辨率
        width, height = map(int, resolution.split('x'))

        # 尝试不同的摄像头后端，优先选择性能最好的
        backends = [
            (cv2.CAP_DSHOW, "DirectShow"),  # Windows优化
            (cv2.CAP_MSMF, "Media Foundation"),  # Windows现代API
            (cv2.CAP_V4L2, "Video4Linux2"),  # Linux
            (cv2.CAP_ANY, "Any Available")  # 兜底选项
        ]

        cap = None
        for backend, name in backends:
            try:
                cap = cv2.VideoCapture(index, backend)
                if cap.isOpened():
                    logger.info(f"使用摄像头后端: {name}")
                    break
                cap.release()
            except Exception as e:
                logger.warning(f"尝试后端 {name} 失败: {e}")
                continue

        if cap is None or not cap.isOpened():
            raise ValueError(f"无法打开摄像头索引 {index}")

        # 极致低延时摄像头配置
        try:
            # 基础参数设置
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            cap.set(cv2.CAP_PROP_FPS, fps)

            # 低延时核心优化 - BUFFERSIZE设置为1帧
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区

            # 格式优化 - 优先MJPEG，降级到YUYV
            formats = [
                cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'),  # MJPEG - 最快
                cv2.VideoWriter_fourcc('Y', 'U', 'Y', 'V'),  # YUYV - 兼容性好
            ]

            for fmt in formats:
                cap.set(cv2.CAP_PROP_FOURCC, fmt)
                # 测试是否设置成功
                if cap.get(cv2.CAP_PROP_FOURCC) == fmt:
                    logger.info(f"摄像头格式设置为MJPG: {fmt}")
                    break

            # 高级低延时设置 - AUTO_EXPOSURE和AUTOFOCUS设置为0
            cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 禁用自动曝光减少延迟
            cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # 禁用自动对焦

            # 验证设置
            actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
            actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
            actual_fps = cap.get(cv2.CAP_PROP_FPS)
            actual_buffer = cap.get(cv2.CAP_PROP_BUFFERSIZE)

            logger.info(f"摄像头配置 - 分辨率: {actual_width}x{actual_height}, "
                       f"FPS: {actual_fps}, 缓冲区: {actual_buffer}")

        except Exception as e:
            logger.warning(f"摄像头高级设置失败，使用默认配置: {e}")

        self.cameras[camera_key] = cap
        return cap
    
    def release_all(self):
        for cap in self.cameras.values():
            cap.release()
        self.cameras.clear()
