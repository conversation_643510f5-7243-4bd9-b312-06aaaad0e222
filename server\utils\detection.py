from ultralytics import YOLO
import cv2
import numpy as np
import torch
import time
import logging

logger = logging.getLogger(__name__)

class Detector:
    def __init__(self, model_path):
        # 检查CUDA可用性和版本
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.use_half = False

        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            cuda_version = torch.version.cuda
            logger.info(f"GPU: {gpu_name}, CUDA: {cuda_version}")

            # 检查是否支持半精度推理
            if torch.cuda.get_device_capability(0)[0] >= 7:  # Volta架构及以上
                self.use_half = True
                logger.info("启用FP16半精度推理加速")
            else:
                logger.info("GPU不支持FP16，使用FP32推理")
        else:
            logger.info("使用CPU推理")

        # 加载模型并优化
        self.model = YOLO(model_path)

        # 移动模型到指定设备 - device=cuda
        if self.device == 'cuda':
            self.model.to(self.device)
            if self.use_half:
                self.model.half()  # 转换为半精度 - half=True

        self.last_counts = {}
        self.class_names = self.model.names

        # 性能统计
        self.inference_times = []
        self.frame_count = 0

        # 模型预热 - 多次预热确保稳定性能
        logger.info("开始模型预热...")
        dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)

        for i in range(5):  # 预热5次
            try:
                start_time = time.time()
                _ = self.model(dummy_frame, verbose=False, half=self.use_half)
                warmup_time = (time.time() - start_time) * 1000
                logger.info(f"预热 {i+1}/5: {warmup_time:.1f}ms")
            except Exception as e:
                logger.warning(f"预热失败 {i+1}/5: {e}")

        logger.info("模型预热完成")

    def detect(self, frame):
        start_time = time.time()

        # 运行YOLOv8检测 - 使用优化参数
        results = self.model(
            frame,
            verbose=False,
            half=self.use_half,
            device=self.device,
            imgsz=640,  # 固定输入尺寸提高效率
            conf=0.5,   # 适中的置信度阈值
            iou=0.45,   # NMS IoU阈值
            max_det=100,  # 限制最大检测数量
            agnostic_nms=False,  # 类别相关的NMS
        )

        # 记录推理时间
        inference_time = (time.time() - start_time) * 1000
        self.inference_times.append(inference_time)
        self.frame_count += 1

        # 每100帧输出一次性能统计
        if self.frame_count % 100 == 0:
            avg_time = np.mean(self.inference_times[-100:])
            fps = 1000 / avg_time if avg_time > 0 else 0
            logger.info(f"检测性能 - 平均推理时间: {avg_time:.1f}ms, FPS: {fps:.1f}")

        # 解析结果
        boxes = results[0].boxes
        self.last_counts = {}

        # 绘制检测结果
        annotated_frame = results[0].plot()

        # 统计各类别数量
        if boxes is not None and len(boxes) > 0:
            for class_id in boxes.cls.unique():
                class_name = self.class_names[int(class_id)]
                count = len(boxes[boxes.cls == class_id])
                self.last_counts[class_name] = count

        return annotated_frame, self.last_counts

    def get_last_counts(self):
        return self.last_counts
