#!/usr/bin/env python3
"""
简化启动脚本 - 一键启动低延时检测系统
"""

import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def check_basic_requirements():
    """检查基本要求"""
    logger.info("检查基本要求...")
    
    # 检查模型文件
    model_path = Path("server/models/best.pt")
    if not model_path.exists():
        logger.error(f"❌ 模型文件不存在: {model_path}")
        logger.info("请确保 YOLOv8 模型文件在 server/models/best.pt")
        return False
    
    logger.info("✅ 模型文件存在")
    
    # 检查FFmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=3)
        logger.info("✅ FFmpeg 可用")
    except:
        logger.error("❌ FFmpeg 未安装或不在 PATH 中")
        logger.info("请安装 FFmpeg 并添加到系统 PATH")
        return False
    
    return True

def start_nginx():
    """启动 Nginx RTMP 服务器"""
    logger.info("启动 Nginx RTMP 服务器...")

    # 使用优化后的nginx配置
    nginx_exe = Path("nginx-rtmp-win32-master/nginx-rtmp-win32-master/nginx.exe")
    nginx_conf = Path("server/nginx-rtmp/nginx.conf").absolute()

    if not nginx_exe.exists():
        logger.error(f"❌ Nginx 可执行文件不存在: {nginx_exe}")
        logger.error("请确保nginx-rtmp-win32-master目录存在")
        return None

    if not nginx_conf.exists():
        logger.error(f"❌ 优化配置文件不存在: {nginx_conf}")
        logger.error("请确保server/nginx-rtmp/nginx.conf存在")
        return None
    
    try:
        # 停止可能运行的nginx
        subprocess.run(['taskkill', '/F', '/IM', 'nginx.exe'], 
                      capture_output=True, shell=True)
        time.sleep(1)
        
        # 启动nginx
        cmd = [str(nginx_exe), '-c', str(nginx_conf)]
        process = subprocess.Popen(
            cmd,
            cwd=str(nginx_exe.parent),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        time.sleep(3)  # 等待启动

        if process.poll() is None:
            logger.info("✅ Nginx RTMP 服务器启动成功")
            logger.info(f"   配置文件: {nginx_conf}")
            logger.info(f"   RTMP端口: 1935")
            logger.info(f"   HTTP端口: 8080")
            return process
        else:
            stdout, stderr = process.communicate()
            logger.error("❌ Nginx 启动失败")
            if stderr:
                logger.error(f"错误信息: {stderr.decode()}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 启动 Nginx 时出错: {e}")
        return None

def start_flask():
    """启动 Flask 应用"""
    logger.info("启动 Flask 应用...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path("server").absolute())
        
        # 启动Flask
        cmd = [sys.executable, "server/app.py"]
        process = subprocess.Popen(cmd, env=env)
        
        time.sleep(3)  # 等待启动
        
        if process.poll() is None:
            logger.info("✅ Flask 应用启动成功")
            logger.info("🌐 API 地址: http://localhost:5000")
            logger.info("📊 状态页面: http://localhost:8080/stat")
            return process
        else:
            logger.error("❌ Flask 启动失败")
            return None
            
    except Exception as e:
        logger.error(f"❌ 启动 Flask 时出错: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 YOLOv8 低延时检测系统 - 简化启动")
    print("=" * 50)
    
    # 检查要求
    if not check_basic_requirements():
        input("按回车键退出...")
        return False
    
    # 启动服务
    nginx_process = start_nginx()
    if not nginx_process:
        input("按回车键退出...")
        return False
    
    flask_process = start_flask()
    if not flask_process:
        # 清理nginx
        try:
            nginx_process.terminate()
        except:
            pass
        input("按回车键退出...")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 系统启动成功!")
    print("=" * 50)
    print("📱 现在可以启动客户端:")
    print("   cd client")
    print("   python main.py")
    print("\n💡 低延时优化已启用:")
    print("   - Nginx chunk_size: 128")
    print("   - 缓冲时间: 100ms")
    print("   - FFmpeg ultrafast + zerolatency")
    print("   - 摄像头缓冲区: 1帧")
    print("   - GPU 加速检测")
    print("\n🌐 服务地址:")
    print("   - API: http://localhost:5000")
    print("   - 状态: http://localhost:8080/stat")
    print("   - RTMP: rtmp://localhost/live/stream")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 监控进程
        while True:
            time.sleep(1)
            
            # 检查进程状态
            if nginx_process.poll() is not None:
                logger.error("❌ Nginx 进程意外退出")
                break
                
            if flask_process.poll() is not None:
                logger.error("❌ Flask 进程意外退出")
                break
                
    except KeyboardInterrupt:
        logger.info("\n🛑 收到停止信号，正在关闭服务...")
        
        # 停止进程
        try:
            flask_process.terminate()
            flask_process.wait(timeout=5)
            logger.info("✅ Flask 服务已停止")
        except:
            flask_process.kill()
            logger.info("🔪 强制停止 Flask 服务")
        
        try:
            nginx_process.terminate()
            nginx_process.wait(timeout=5)
            logger.info("✅ Nginx 服务已停止")
        except:
            nginx_process.kill()
            logger.info("🔪 强制停止 Nginx 服务")
        
        logger.info("👋 服务已全部停止")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
