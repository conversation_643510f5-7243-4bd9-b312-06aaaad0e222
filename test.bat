@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🧪 YOLOv8 低延时检测系统 - 性能测试
echo ================================================
echo.

echo [1] 运行完整性能测试 (推荐)
echo [2] 运行简单测试
echo [3] 检测硬件能力
echo.

set /p choice="请选择测试类型 (1-3): "

if "%choice%"=="1" (
    echo 正在运行完整性能测试...
    python performance_test.py
) else if "%choice%"=="2" (
    echo 正在运行简单测试...
    python simple_test.py
) else if "%choice%"=="3" (
    echo 正在检测硬件能力...
    python -c "import sys; sys.path.append('server'); from performance_test import PerformanceTester; tester = PerformanceTester(); tester.test_hardware_capabilities()"
) else (
    echo 无效选择，运行默认测试...
    python performance_test.py
)

echo.
echo 测试完成！
pause
