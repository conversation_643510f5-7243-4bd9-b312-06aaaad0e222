#!/usr/bin/env python3
"""
API测试脚本 - 验证project3整合后的功能
"""

import requests
import json
import time

def test_api():
    """测试API功能"""
    base_url = "http://127.0.0.1:5000/api"
    
    print("🧪 开始API功能测试...")
    
    # 1. 测试状态API
    print("\n1️⃣ 测试状态API...")
    try:
        response = requests.get(f"{base_url}/status")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   ❌ 状态API测试失败: {e}")
        return False
    
    # 2. 测试启动检测API
    print("\n2️⃣ 测试启动检测API...")
    try:
        data = {
            'camera_index': 0,
            'resolution': '640x640',
            'fps': 30
        }
        response = requests.post(f"{base_url}/start", json=data)
        print(f"   状态码: {response.status_code}")
        result = response.json()
        print(f"   响应: {result}")
        
        if result.get('status') == 'success':
            print("   ✅ 检测启动成功")
        else:
            print("   ❌ 检测启动失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动API测试失败: {e}")
        return False
    
    # 3. 等待一下，然后检查运行状态
    print("\n3️⃣ 等待检测运行...")
    time.sleep(3)
    
    try:
        response = requests.get(f"{base_url}/status")
        result = response.json()
        print(f"   运行状态: {result.get('status')}")
        print(f"   计数结果: {result.get('counts', {})}")
        
        if result.get('status') == 'running':
            print("   ✅ 检测正在运行")
        else:
            print("   ⚠️ 检测状态异常")
            
    except Exception as e:
        print(f"   ❌ 状态检查失败: {e}")
    
    # 4. 测试停止检测API
    print("\n4️⃣ 测试停止检测API...")
    try:
        response = requests.get(f"{base_url}/stop")
        print(f"   状态码: {response.status_code}")
        result = response.json()
        print(f"   响应: {result}")
        
        if result.get('status') == 'success':
            print("   ✅ 检测停止成功")
        else:
            print("   ❌ 检测停止失败")
            
    except Exception as e:
        print(f"   ❌ 停止API测试失败: {e}")
    
    print("\n🎉 API测试完成!")
    return True

if __name__ == "__main__":
    test_api()
